# Prompt: Fix Duplicate Habits After Reordering

## 1. Objective & Context

The objective is to fix a critical bug where reordering habits causes duplicates to appear on the main habits screen, as shown in `52.jpg`. While the reordering and persistence logic is now working, the UI is incorrectly displaying multiple copies of the same habits after the user returns from the reordering screen (`51.jpg`).

**Root Cause Analysis:**
This bug is caused by an error in how the local data is managed on the main habits screen. When the user returns to this screen, a data refresh is triggered. However, instead of clearing the old list of habits before adding the newly fetched, reordered habits, the new list is simply being appended to the existing one. This results in the list growing with duplicate entries after each reordering operation.

## 2. Detailed Implementation Plan

The fix requires a single, crucial change in the data handling logic within the state management component for the **main habits screen**.

**Task 2.1: Implement a "Clear and Replace" Strategy**

- Locate the section of code responsible for observing and receiving the updated list of habits from Firestore. This is likely within a `LiveData` or `StateFlow` observer in the main screen's state management component.
- Inside this observer, before you add the new habits to the local list that populates your UI adapter, you **must first clear the existing list completely**.
- The correct sequence of operations must be:
  1.  Clear the local list that holds the habits for the UI.
  2.  Add all the newly fetched habits to this now-empty list.
  3.  Notify the list adapter that the data set has changed.

By ensuring the local list is always cleared before being repopulated, we guarantee that it only ever contains the single, most current source of truth from the database.

## 3. Verification Plan

Please follow these steps meticulously to confirm the fix.

1.  Start the application and navigate to the main habits screen. Note the exact number of habits present (e.g., 2 habits: "Hab1", "Hav2").
2.  Navigate to the screen for reordering habits (`51.jpg`).
3.  Swap the order of the two habits.
4.  Press the back button to return to the main habits screen.
5.  **Verify (CRITICAL):** The habits are now displayed in the new order, and crucially, there are **no duplicates**. The screen should still show exactly 2 habits.
6.  Repeat the process: go back to the reordering screen, change the order again, and return to the main screen.
7.  **Verify:** The order updates correctly, and the habit count remains the same. No new duplicates have appeared.
8.  Close and restart the application to ensure the state is clean.
9.  **Verify:** The last saved order is present, and there are no duplicates.

## 4. Mandatory Development Guidelines

- **Refer to the Style Guide:** Before starting any feature, always consult the project's style guide for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns. It is the single source of truth for styling decisions.
- **Study the Reference Project:** Prior to implementation, review the reference project to understand how similar features have been approached to maintain consistency and avoid duplications or contradictions. The reference project serves as a blueprint for implementation. This step is mandatory. Do not proceed to implementation without this step.The reference project is located in the `uhabits-dev` folder.
- **Understand the Existing Project Structure:** Before writing any code, spend time exploring and understanding how the current system is structured. Even for new features, existing components or utility functions may be reusable. Integrate changes cleanly into the existing architecture instead of creating disconnected code.
- **Maintain a Clean Codebase:** After implementing features, remove any temporary, test, or duplicate files, folders, or unused components that were created during development. Keep the codebase organized and clutter-free.
- **Pause If There Is Any Confusion:** If at any point the requirements are unclear, do not proceed based on assumptions. Immediately pause and seek clarification. It is better to get clarity than to redo or fix avoidable mistakes later.
- **Remove Unused Old Implementations:** As part of final review, identify and delete any old, unused code that was implemented earlier but is no longer in use. This includes obsolete modules, features, or legacy logic.
