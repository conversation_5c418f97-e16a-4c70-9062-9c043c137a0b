package com.example.habits9.ui.habitreorder

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.DragHandle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.compositeOver
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel

// Colors from style guide
private val BackgroundDark = Color(0xFF121826)
private val TextPrimary = Color(0xFFE2E8F0)
private val TextSecondary = Color(0xFFA0AEC0)
private val AccentPrimary = Color(0xFF81E6D9)
private val SurfaceVariantDark = Color(0xFF1A202C)
private val DividerColor = Color(0xFF2D3748)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HabitReorderScreen(
    onBackClick: () -> Unit,
    viewModel: HabitReorderViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val hapticFeedback = LocalHapticFeedback.current

    // Save and clear state when navigating back to ensure persistence
    val handleBackClick = {
        viewModel.saveOrder()
        viewModel.clearReorderedState()
        onBackClick()
    }

    // Save and clear state when screen is disposed (e.g., system back button)
    DisposableEffect(Unit) {
        onDispose {
            viewModel.saveOrder()
            viewModel.clearReorderedState()
        }
    }

    // Drag state
    var draggedItemIndex by remember { mutableStateOf(-1) }
    var dragOffset by remember { mutableStateOf(Offset.Zero) }
    val listState = rememberLazyListState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Reorder Habits",
                        color = TextPrimary,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = handleBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = TextPrimary
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = SurfaceVariantDark
                )
            )
        },
        containerColor = BackgroundDark
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (uiState.habits.isEmpty()) {
                // Empty state
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(32.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "You have no habits to organize.",
                        color = TextPrimary,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Go back and add a new habit to get started!",
                        color = TextSecondary,
                        fontSize = 14.sp
                    )
                }
            } else {
                // Habit list
                LazyColumn(
                    state = listState,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    itemsIndexed(
                        items = uiState.habits,
                        key = { index, habit -> "${habit.uuid}_reorder_$index" }
                    ) { index, habit ->
                        val isDraggingThisItem = draggedItemIndex == index
                        
                        HabitReorderItem(
                            habitName = habit.name,
                            isDragging = isDraggingThisItem,
                            dragOffset = if (isDraggingThisItem) dragOffset else Offset.Zero,
                            onDragStart = {
                                draggedItemIndex = index
                                dragOffset = Offset.Zero
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            },
                            onDrag = { dragAmount ->
                                dragOffset += dragAmount
                                
                                // Calculate drop target based on drag position
                                val visibleItems = listState.layoutInfo.visibleItemsInfo
                                val currentItemInfo = visibleItems.find { it.index == index }
                                
                                if (currentItemInfo != null) {
                                    val itemCenterY = currentItemInfo.offset + currentItemInfo.size / 2 + dragOffset.y
                                    
                                    // Find the item we're hovering over
                                    val targetItem = visibleItems.find { item ->
                                        val itemTop = item.offset
                                        val itemBottom = item.offset + item.size
                                        itemCenterY >= itemTop && itemCenterY <= itemBottom
                                    }
                                    
                                    if (targetItem != null && targetItem.index != index) {
                                        // Reorder the items
                                        viewModel.moveHabit(index, targetItem.index)
                                        draggedItemIndex = targetItem.index
                                        dragOffset = Offset.Zero
                                        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                    }
                                }
                            },
                            onDragEnd = {
                                draggedItemIndex = -1
                                dragOffset = Offset.Zero
                                viewModel.saveOrder()
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun HabitReorderItem(
    habitName: String,
    isDragging: Boolean,
    dragOffset: Offset,
    onDragStart: () -> Unit,
    onDrag: (Offset) -> Unit,
    onDragEnd: () -> Unit,
    modifier: Modifier = Modifier
) {
    // Enhanced visual feedback with smooth animations
    val animatedElevation by animateDpAsState(
        targetValue = if (isDragging) 12.dp else 2.dp,
        animationSpec = tween(durationMillis = 200),
        label = "elevation"
    )

    val animatedScale by animateFloatAsState(
        targetValue = if (isDragging) 1.02f else 1f,
        animationSpec = tween(durationMillis = 200),
        label = "scale"
    )

    val animatedAlpha by animateFloatAsState(
        targetValue = if (isDragging) 0.9f else 1f,
        animationSpec = tween(durationMillis = 200),
        label = "alpha"
    )

    Card(
        modifier = modifier
            .fillMaxWidth()
            .offset(y = dragOffset.y.dp)
            .scale(animatedScale)
            .alpha(animatedAlpha)
            .shadow(
                elevation = animatedElevation,
                shape = RoundedCornerShape(12.dp)
            )
            .pointerInput(Unit) {
                detectDragGestures(
                    onDragStart = { onDragStart() },
                    onDrag = { _, dragAmount -> onDrag(dragAmount) },
                    onDragEnd = { onDragEnd() }
                )
            },
        colors = CardDefaults.cardColors(
            containerColor = if (isDragging)
                AccentPrimary.copy(alpha = 0.1f).compositeOver(SurfaceVariantDark)
            else
                SurfaceVariantDark
        ),
        shape = RoundedCornerShape(12.dp),
        border = if (isDragging) BorderStroke(2.dp, AccentPrimary.copy(alpha = 0.3f)) else null
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Enhanced drag handle with better visual feedback
            Icon(
                imageVector = Icons.Default.DragHandle,
                contentDescription = "Drag to reorder",
                tint = if (isDragging) AccentPrimary else TextSecondary,
                modifier = Modifier
                    .size(24.dp)
                    .padding(2.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Text(
                text = habitName,
                color = if (isDragging) TextPrimary.copy(alpha = 0.9f) else TextPrimary,
                fontSize = 16.sp,
                fontWeight = if (isDragging) FontWeight.SemiBold else FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )
        }
    }
}
